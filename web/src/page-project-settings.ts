import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlDialog } from '@shoelace-style/shoelace';
import { live } from 'lit/directives/live.js';
import slugify from 'slugify';
import { api } from './api.js';
import { docLinkStyles, linkStyles, pageStyles } from './components/cssstyles.js';
import { GithubInstallation } from './components/pick-github-installation.js';
import { GithubPat } from './components/pick-github-pat.js';
import { PickProjectName, ProjectNamePicked } from './components/pick-project-name.js';
import { ProjectVisibilityEvent } from './components/pick-project-visibility.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts, FKProjectChanged, TaskEvent } from './contexts.js';
import { githubLinks } from './githubLinks.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageOrganization } from './page-organization.js';
import { RouteConfig } from './router.js';
import { assert } from './utils.js';

@customElement('page-project-settings')
export class PageProjectSettings extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string, tab?: 'general'|'usage' }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'settings'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/settings',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-settings
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-settings>
      `),
    }];
  }

  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  
  @query('#link-dialog') private _linkDialog?: SlDialog;
  @query('pick-project-name') private _projectName?: PickProjectName;

  @state() private _githubPat?: GithubPat;
  @state() private _githubApp?: GithubInstallation;
  @state() private _projectRename?: ProjectNamePicked;

  private _fetchedRepositoryStats = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug] as const,
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      return await api.project.fetchRepositoryStats.GET({ orgSlug, projectSlug }, { signal });
    },
  });

  private _linkSourceTask = new Task(this, {
    autoRun: false,
    args: () => [this.orgSlug, this.projectSlug, this._githubApp, this._githubPat] as const,
    task: async ([orgSlug, projectSlug, githubApp, githubPat], { signal }) => {
      assert(orgSlug && projectSlug);
      assert(githubApp || githubPat);
      if (githubApp) {
        await api.project.setProjectSourceWithGithubInstallation.POST({
          orgSlug, projectSlug,
          sourceOwner: githubApp.owner,
          sourceRepo: githubApp.repo,
        }, { signal });
      } else if (githubPat) {
        await api.project.setProjectSourceWithGithubPAT.POST({
          orgSlug, projectSlug,
          sourceOwner: githubPat.owner,
          sourceRepo: githubPat.repo,
          personalAccessToken: githubPat.accessToken,
        }, { signal });
      }
      this.dispatchEvent(new FKProjectChanged({ orgSlug, projectSlug }));
      this._linkDialog?.hide();
    },
  });

  private async _renameProject() {
    const newProjectName = this._projectRename?.projectName;
    const orgSlug = this.orgSlug;
    const projectSlug = this.projectSlug;
    if (!projectSlug || !newProjectName || !orgSlug)
      return;
    const newProjectSlug = slugify.default(newProjectName);
    const success = await api.project.setProjectName.POST({
      orgSlug,
      projectSlug,
      newProjectName,
      newProjectSlug,
    }).then(() => true).catch(e => {
      console.error(e);
      return false;
    })
    this._projectName?.setStatus(success, success ? 'Project renamed' : 'Failed to rename project');
    if (success) {
      this._projectRename = undefined;
      await this._router?.navigate(PageProjectSettings.url({
        orgSlug: orgSlug,
        projectSlug: newProjectSlug,
      }));
    }
  }

  private async _onVisibilityChanged(event: ProjectVisibilityEvent) {
    const project = this._project;
    if (!project)
      return;
    await api.project.setVisibility.POST({
      orgSlug: project.org.orgSlug,
      projectSlug: project.projectSlug,
      visibility: event.visibility,
    });
    this.dispatchEvent(new FKProjectChanged({
      orgSlug: project.org.orgSlug,
      projectSlug: project.projectSlug,
    }));
  }

  render() {
    if (!this._project)
      return nothing;

    return html`
      <sl-dialog id=link-dialog style="--width: 75ch;" label="Link to a Git repository">
        <div>Changing linked git repository is a <b>non-destructive</b> operation.</div>
        <sl-tab-group>
          <sl-tab slot="nav" panel="app">Github App</sl-tab>
          <sl-tab slot="nav" panel="pat">Github Personal Access Token</sl-tab>

          <sl-tab-panel name="app">
            <pick-github-installation
              @github-installation=${(event: TaskEvent<GithubInstallation>) => this._githubApp = event.data }
            ></pick-github-installation>
          </sl-tab-panel>
          <sl-tab-panel name="pat">
            <pick-github-pat @github-pat=${(event: TaskEvent<GithubPat>) => this._githubPat = event.data }></pick-github-pat>
          </sl-tab-panel>
        </sl-tab-group>
        <sl-button ?disabled=${!this._githubApp && !this._githubPat} slot="footer" variant="primary" @click=${() => this._linkSourceTask.run()}>Link</sl-button>
      </sl-dialog>

      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"settings"}
      ></project-header>
      <app-body user-role=${this._project.access}>
        <h1>Project Settings</h1>

        <form-section is-editor label="Flakiness Access Token">
          <sl-copy-button id=copy-button value=${this._project.readWriteAccessToken}></sl-copy-button>
          <sl-tag size=medium>${this._project.readWriteAccessToken}</sl-tag>
        </form-section>

        <form-section label="Project name">
          <pick-project-name
            ?disabled=${this._project.access !== 'editor'}
            .orgSlug=${this._project.org.orgSlug}
            .initial=${live(this._project.projectName)}
            @project-name=${(event: TaskEvent<ProjectNamePicked>) => this._projectRename = event.data }
          >
            <sl-button ?disabled=${!this._projectRename?.projectName} @click=${() => this._renameProject()}>Rename</sl-button>
          </pick-project-name>
        </form-section>

        <form-section label="Project Visibility" style="margin-top: -var(--sl-spacing-medium);">
          <pick-project-visibility
            ?disabled=${this._project.access !== 'editor'}
            .initial=${this._project?.visibility}
            @project-visibility=${this._onVisibilityChanged}
          ></pick-project-visibility>
        </form-section>

        <form-section label="Git Repository">
          <p>
            <h-box>
              <a-ext href=${githubLinks.projectUrl(this._project)}>
                <h-box style="gap: var(--sl-spacing-2x-small);">
                  <sl-icon name=github></sl-icon>
                  <span>${this._project.sourceOwner}/${this._project.sourceRepo}</span>
                </h-box>
              </a-ext>
              <sl-button size=small is-editor @click=${() => this._linkDialog?.show()}>Link to another repository</sl-button>
            </h-box>
          </p>
          <p>
            ${this._renderLastFetchAttempt(this._project)}
          </p>
        </form-section>

        <form-section is-editor>
          <sl-divider></sl-divider>
          <h1>Danger Zone</h1>
          <sl-button @click=${this._deleteProject.bind(this)} variant=danger>Delete Project</sl-button>
        </form-section>

      </app-body>
      <app-footer></app-footer>
    `
  }

  private _renderLastFetchAttempt(project: WireTypes.Project) {
    const { commits, branches, pullRequests } = this._fetchedRepositoryStats.value ?? {};
    if (!project.sourceLastFetchHTTPCode || !project.sourceLastFetchTime || commits === undefined || branches === undefined)
      return html`<span>fetching commits right now...</span>`;
    const relativeTime = html`
      <sl-tooltip>
        <sl-format-date slot="content" month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(project.sourceLastFetchTime).toISOString()}></sl-format-date>
        <b><sl-relative-time date=${new Date(project.sourceLastFetchTime).toISOString()}></sl-relative-time></b>
      </sl-tooltip>
    `;

    const info = html`Fetched <b>${commits}</b> commits, <b>${branches}</b> branches and <b>${pullRequests}</b> pull requests.`;
    const attempt = (project.sourceLastFetchHTTPCode === 200) ?
      html`<b>Successfully</b> pulled history ${relativeTime}` :
      html`<sl-tooltip content="Github responded with HTTP ${project.sourceLastFetchHTTPCode}"><b>Failed</b></sl-tooltip> to pull history ${relativeTime}`
    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <div>${info}</div>
        <div>${attempt}</div>
      </v-box>
    `
  }

  async _deleteProject() {
    const { orgSlug, projectSlug } = this;
    if (!orgSlug || !projectSlug || !this._router)
      return;
    const value = prompt(`Type in "${orgSlug}/${projectSlug}" to confirm deletion`)
    if (value?.toLowerCase() !== (orgSlug + '/' + projectSlug).trim().toLowerCase())
      return;
    await api.project.delete.POST({ orgSlug, projectSlug });
    this._router.navigate(PageOrganization.url({ orgSlug } ));
  }

  static styles = [linkStyles, githubMarkdownCSS, docLinkStyles, pageStyles, css`
    a-ext {
      color: rgb(9, 105, 218);
    }
    [user-role=viewer] {
      *:is([is-editor]) {
        display: none;
      }
    }
  `];
}
