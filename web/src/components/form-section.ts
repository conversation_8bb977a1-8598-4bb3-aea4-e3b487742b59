import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('form-section')
class FormSection extends LitElement {
  @property({ }) label?: string;

  override render() {
    return html`
      <header>
        <h-box class=title>
          <div>${this.label}</div>
          <div><slot name=label></slot></div>
        </h-box>
        <div class=description><slot name=description></slot></div>
      </header>
      <div>
        <slot></slot>
      </div>
    `;
  }

  static styles = [css`

    header {
      margin-bottom: var(--sl-spacing-small);
    }
    .title {
      font-weight: var(--sl-font-weight-bold);
      margin-bottom: var(--sl-spacing-small);
    }
    .description {
      color: var(--sl-color-neutral-500);
    }
  `];
}