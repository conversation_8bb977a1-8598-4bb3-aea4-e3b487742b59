import { ProjectPublicId } from "@flakiness/database";
import { SharedCacheStore } from "../common/caches/cache.js";
import { SyncComputationCache } from "../common/computationCache.js";
import { Histogram } from "../common/stats/histogram.js";
import { Stats } from "../common/stats/stats.js";
import { StatsAnalyzer } from "../common/stats/statsAnalyzer.js";
import { TestOutcomes as T } from "../common/stats/testOutcomes.js";
import { TestsReport } from "../common/stats/testsReport.js";
import { WireTypes } from "../common/wireTypes.js";
import { Git } from "./git.js";
import { ProjectQueryService } from "./projectQueryService.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Stats } from "./s3layout.js";
import { ServerTiming } from "./serverTiming.js";

export class QueryService {
  private _projectQueryService = new SyncComputationCache<ProjectPublicId, ProjectQueryService>({
    size: 50,
    etag: ppid => ppid,
    compute: (ppid) => new ProjectQueryService(ppid, this._memoryCache, this._serverTiming, this._getReportIndex, this._getStatsAnalyzer, this._getRepo),
  });

  constructor(
    private _memoryCache: SharedCacheStore,
    private _serverTiming: ServerTiming,
    private _getReportIndex: (p: ProjectPublicId) => Promise<ReportIndex.ReportIndex>,
    private _getStatsAnalyzer: (s3id: S3Stats.Id) => Promise<StatsAnalyzer>,
    private _getRepo: (p: ProjectPublicId) => Promise<Git.Repository|undefined>,
  ) {
  }

  public async runEnvironments(ppid: ProjectPublicId, commitOptions: WireTypes.ListCommitOptions): Promise<WireTypes.RunEnvironment[]> {
    return await this._projectQueryService.get(ppid).runEnvironments(commitOptions);
  }

  public async runEnvironmentsForTest(ppid: ProjectPublicId, commitOptions: WireTypes.ListCommitOptions, testId: Stats.TestId): Promise<WireTypes.RunEnvironment[]> {
    return await this._projectQueryService.get(ppid).runEnvironmentsForTest(commitOptions, testId);
  }

  public async reportCounters(ppid: ProjectPublicId, options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
  }): Promise<{ testOutcomes: T.TestOutcomeCounts, tests: number, timelines: number, errors: number, annotations: number, tags: number }> {
    return await this._projectQueryService.get(ppid).reportCounters(options);
  }

  public async testsReport(ppid: ProjectPublicId, options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    dailyOutcomesDays: number,
  }): Promise<TestsReport> {
    return await this._projectQueryService.get(ppid).testsReport(options);
  }

  public async commitsReport(ppid: ProjectPublicId, options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    testId?: Stats.TestId,
    days: WireTypes.Day[],
    regressionWindowDays: number,
  }): Promise<WireTypes.CommitStats[]> {
    return await this._projectQueryService.get(ppid).commitsReport(options);
  }

  public async dailyUnfilteredTestOutcomes(ppid: ProjectPublicId, options: {
    head: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    cutoffTestIndex: Stats.TestIndex,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes[], commitCounts: number[], durations: Histogram.CompressedHistogram[][] }[]> {
    return await this._projectQueryService.get(ppid).dailyUnfilteredTestOutcomes(options);
  }

  public async dailyOutcomes(ppid: ProjectPublicId, options: {
    head: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    timelineSplit: WireTypes.JSONTimelineSplit,
    resultDays: number,
    testId?: Stats.TestId,
  }): Promise<WireTypes.DayOutcome[]> {
    return await this._projectQueryService.get(ppid).dailyOutcomes(options);
  }

  public async commitUnfilteredTestOutcomes(ppid: ProjectPublicId, options: {
    regressionWindowDays: number,
    days: WireTypes.Day[],
    cutoffTestIndex: Stats.TestIndex,
    commitId: Stats.CommitId,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes }[]> {
    return await this._projectQueryService.get(ppid).commitUnfilteredTestOutcomes(options);
  }
}