
import { ProjectPublicId } from "@flakiness/database";
import { xxHashObject } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import debug from 'debug';
import { SharedCacheStore } from "../common/caches/cache.js";
import { SyncComputationCache } from "../common/computationCache.js";
import { Query } from "../common/fql/query.js";
import { Ranges } from "../common/ranges.js";
import { CommitAnalyzer } from "../common/stats/commitAnalyzer.js";
import { Histogram } from "../common/stats/histogram.js";
import { HistoryAnalyzer } from "../common/stats/historyAnalyzer.js";
import { SpanAnalyzer } from "../common/stats/spanAnalyzer.js";
import { Stats } from "../common/stats/stats.js";
import { StatsAnalyzer } from "../common/stats/statsAnalyzer.js";
import { FilterContext, TestIndex } from "../common/stats/testIndex.js";
import { TestOutcomes as T } from "../common/stats/testOutcomes.js";
import { TestsReport } from "../common/stats/testsReport.js";
import { TimelineCommitReport } from "../common/stats/timelineCommitReport.js";
import { TimelineTestsReport } from "../common/stats/timelineTestsReport.js";
import { Timeline } from "../common/timeline/timeline.js";
import { TimelineSplit } from "../common/timeline/timelineSplit.js";
import { WireTypes } from "../common/wireTypes.js";
import { Git } from "./git.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Stats } from "./s3layout.js";
import { ServerTiming } from "./serverTiming.js";

const log = debug('fk:query_service');

export class ProjectQueryService {
  private _commitAnalyzers: SyncComputationCache<{
    commitId: Stats.CommitId,
    stats: StatsAnalyzer,
  }, CommitAnalyzer>;

  private _spanAnalyzers: SyncComputationCache<CommitAnalyzer[], SpanAnalyzer>;

  private _historyAnalyzers: SyncComputationCache<{
    historyDays: SpanAnalyzer[],
    regressionWindowDays: number,
  }, HistoryAnalyzer>;

  private _filterContexts: SyncComputationCache<{
    testIndex: TestIndex,
    fql: Query,
  }, FilterContext>;

  private _timelineTestsReport: SyncComputationCache<{
    timeline: Timeline,
    span: SpanAnalyzer,
    history: HistoryAnalyzer,
  }, TimelineTestsReport>;

  private _timelineCommitReport: SyncComputationCache<{
    timeline: Timeline,
    commitAnalyzer: CommitAnalyzer,
    history: HistoryAnalyzer,
  }, TimelineCommitReport>;

  private _commitsReport: SyncComputationCache<{
    commits: WireTypes.Commit[],
    analyzers: CommitAnalyzer[],
    history: HistoryAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
  }, WireTypes.CommitStats[]>;

  private _reportCounters: SyncComputationCache<{
    span: SpanAnalyzer,
    history: HistoryAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
  }, {
    testOutcomes: T.TestOutcomeCounts,
    tests: number,
    timelines: number,
    errors: number,
    annotations: number,
    tags: number
  }>;

  private _testsReport: SyncComputationCache<{
    testIndex: TestIndex,
    span: SpanAnalyzer,
    history: HistoryAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
    dailyOutcomesDays: number,
  }, TestsReport>;

  private _dailyOutcomes: SyncComputationCache<{
    testIndex: TestIndex,
    history: HistoryAnalyzer,
    timelines: Timeline[],
    testId?: Stats.TestId,
    resultDays: number,
  }, WireTypes.DayOutcome[]>;

  constructor(
    private _ppid: ProjectPublicId,
    memoryCache: SharedCacheStore,
    serverTiming: ServerTiming,
    private _getReportIndex: (p: ProjectPublicId) => Promise<ReportIndex.ReportIndex>,
    private _getStatsAnalyzer: (s3id: S3Stats.Id) => Promise<StatsAnalyzer>,
    private _getRepo: (p: ProjectPublicId) => Promise<Git.Repository|undefined>,
  ) {
    this._commitAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 1000, // 1000 commits cached per project.
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.commitAnalyzers', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.commitAnalyzers`, this._ppid, until - since, cacheHit, cacheSize);
      },
      etag: ({ commitId, stats }) => stats.getCommitAnalyzerEtag(commitId),
      compute({ commitId, stats }, etag) {
        return stats.getCommitAnalyzer(commitId);
      },
    });

    this._spanAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 300, // 4 months = 120 days. 300 should be enough for 2 completely distinct branches.
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.spanAnalyzers', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.spanAnalyzers`, this._ppid, until - since, cacheHit, cacheSize);
      },
      etag: commits => SpanAnalyzer.etag(commits),
      compute: (commits, etag) => new SpanAnalyzer(commits, etag),
    });

    this._filterContexts = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ testIndex, fql }) => TestIndex.etagFilterContext(testIndex, fql),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getFilterContext', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getFilterContext`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ testIndex, fql }, etag) => {
        return testIndex.createFilterContext(fql, etag);
      }
    });

    this._historyAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ historyDays, regressionWindowDays }) => HistoryAnalyzer.etag(historyDays, regressionWindowDays),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getHistoryAnalyzer', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getHistoryAnalyzer`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ historyDays, regressionWindowDays }, etag) => {
        return new HistoryAnalyzer(historyDays, regressionWindowDays, etag);
      },
    });

    this._timelineTestsReport = new SyncComputationCache({
      cache: memoryCache,
      size: Timeline.MAX_TIMELINES,
      etag: ({ timeline, span, history }) => xxHashObject({
        timeline: timeline.etag(),
        span: span.etag(),
        history: history.etag(),
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getTestsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getTestsReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ timeline, span, history }) => {
        const lastCommit = span.commits.at(-1);
        const unhealthyTests = lastCommit ? history.unhealthyTests(timeline, lastCommit.commitId) : T.EMPTY_TESTS;
        return TimelineTestsReport.create(timeline, span, unhealthyTests);
      },
    });

    this._timelineCommitReport = new SyncComputationCache({
      cache: memoryCache,
      size: Timeline.MAX_TIMELINES * 1000,
      etag: ({ timeline, commitAnalyzer, history }) => xxHashObject({
        timeline: timeline.etag(),
        commitAnalyzer: commitAnalyzer.etag(),
        history: history.etag(),
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.timelineCommitReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.timelineCommitReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ timeline, commitAnalyzer, history }) => {
        const unhealthyTests = history.unhealthyTests(timeline, commitAnalyzer.commitId);
        return TimelineCommitReport.create(timeline, commitAnalyzer, unhealthyTests);
      },
    });

    this._commitsReport = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ analyzers, timelines, history, filter }) => xxHashObject({
        analyzers: SpanAnalyzer.etag(analyzers),
        timelines: Timeline.etagAll(timelines),
        history: history.etag(),
        filter: filter.etag,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.commitsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.commitsReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ analyzers, timelines, history, filter, commits }) => {
        return commits.map((commit, idx) => {
          const reports = timelines.map(timeline => this._timelineCommitReport.get({
            commitAnalyzer: analyzers[idx],
            history,
            timeline,
          }).applyFilter(filter));
          return TimelineCommitReport.wireCommitStats(commit, reports);
        })
      },
    });

    this._reportCounters = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ span, history, timelines, filter }) => xxHashObject({
        span: span.etag(),
        history: history.etag(),
        timelines: Timeline.etagAll(timelines),
        filter: filter.etag,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getReportCounter', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getReportCounter`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ span, history, timelines, filter }) => {
        const result = {
          testOutcomes: T.newOutcomeCounts(),
          errors: new Set<Stats.ErrorId>(),
          timelines: 0,
          tags: new Set<string>,
          annotations: new Set<Stats.AnnotationId>(),
        }
        for (const timeline of timelines) {
          const report = this._timelineTestsReport.get({
            span,
            history,
            timeline,
          }).applyFilter(filter);
          if (T.isEmptyOutcomes(report.outcomes))
            continue;
          ++result.timelines;
          T.accumulateOutcomeCounts(result.testOutcomes, T.newOutcomeCounts(report.outcomes));
          for (const error of report.errorTests.keys())
            result.errors.add(error.errorId);
          for (const tag of report.tagTests.keys())
            result.tags.add(tag);
          for (const annotation of report.annotationTests.keys())
            result.annotations.add(annotation.annotationId);
        }
        return {
          testOutcomes: result.testOutcomes,
          errors: result.errors.size,
          tests: T.sumAllCounts(result.testOutcomes),
          timelines: result.timelines,
          tags: result.tags.size,
          annotations: result.annotations.size,
        };
      },
    });

    this._testsReport = new SyncComputationCache({
      cache: memoryCache,
      size: 10, // Not more than 10 reports are stored per project.
      etag: ({ span, history, timelines, filter, dailyOutcomesDays }) => xxHashObject({
        span: span.etag(),
        history: history.etag(),
        timelines: Timeline.etagAll(timelines),
        filter: filter.etag,
        dailyOutcomesDays,
      }),
      onGetTiming: (input, key, since, until, cacheHit) => {
        serverTiming.recordTraceEvent('q.getTestsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getTestsReport`, this._ppid, until - since, cacheHit, this._testsReport.cacheSize());
      },
      compute: ({ testIndex, span, history, timelines, filter, dailyOutcomesDays }) => {
        const timelineReports = timelines.map(timeline => {
          const report = this._timelineTestsReport.get({
            timeline,
            span,
            history,
          });
          const intermediateOutcomes = history
            .days()
            .slice(0, dailyOutcomesDays)
            .map((day, dayIdx) => history.dayStats(timeline, dayIdx, true /* computeDurations */));
          return report.applyFilter(filter).setIntermediateStats(intermediateOutcomes);
        });
        return new TestsReport(testIndex, timelineReports);
      },
    });

    this._dailyOutcomes = new SyncComputationCache({
      cache: memoryCache,
      size: 100,
      etag: ({ testIndex, history, timelines, testId, resultDays }) => xxHashObject({
        testIndex: testIndex.etag(),
        history: history.etag(),
        timelines: Timeline.etagAll(timelines),
        testId,
        resultDays,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getDailyOutcomes', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getDailyOutcomes`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ testIndex, history, timelines, testId, resultDays }) => {
        const filter = this._filterContexts.get({ testIndex, fql: testId ? Query.create({ testId }) : Query.EMPTY });
        return history.days().slice(0, resultDays).map((day, dayIdx) => {
          if (!day.commits.length)
            return 'idle';
          const acc = T.newOutcomeCounts();
          for (const timeline of timelines) {
            let c = history.dayStats(timeline, dayIdx, false /* computeDurations */).testOutcomes;
            c = T.intersectRanges(c, filter.testsMask);
            T.accumulateOutcomeCounts(acc, T.newOutcomeCounts(c));
          }
          if (acc.regressed)
            return 'regressed';
          if (acc.unexpected)
            return 'unexpected';
          const total = acc.regressed + acc.unexpected + acc.flaked + acc.expected + acc.skipped;
          const flakinessRatio = 100 * acc.flaked / total;
          if (acc.flaked && flakinessRatio > 1.5)
            return 'flaked';
          if (acc.expected)
            return 'expected';
          if (acc.flaked)
            return 'flaked';
          if (acc.skipped)
            return 'skipped';
          return 'untested';
        });
      }
    });
  }

  public async runEnvironments(commitOptions: WireTypes.ListCommitOptions): Promise<WireTypes.RunEnvironment[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const commits = repo?.iterator(commitOptions.head).collect(commitOptions) ?? [];
    const span = await this._fetchSpan(index, commits)
    return CommitAnalyzer.runEnvironments(span.commits);
  }

  public async runEnvironmentsForTest(commitOptions: WireTypes.ListCommitOptions, testId: Stats.TestId): Promise<WireTypes.RunEnvironment[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);

    const commits = repo?.iterator(commitOptions.head).collect(commitOptions) ?? [];
    const span = await this._fetchSpan(index, commits)
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.create({ testId }) });
    if (Ranges.cardinality(filter.testsMask) !== 1)
      return [];
    return CommitAnalyzer.runEnvironmentsForTest(span.commits, filter.testsMask[0]);
  }

  public async reportCounters(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
  }): Promise<{ testOutcomes: T.TestOutcomeCounts, tests: number, timelines: number, errors: number, annotations: number, tags: number }> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const commits = repo?.iterator(options.commitOptions.head).collect(options.commitOptions) ?? [];
    const lastCommit = commits.at(-1);
    if (!lastCommit)
      return { testOutcomes: T.newOutcomeCounts(), tests: 0, timelines: 0, errors: 0, annotations: 0, tags: 0 };

    const [span, history] = await Promise.all([
      this._fetchSpan(index, commits),
      this._historyAnalyzer(index, repo, {
        head: options.commitOptions.head,
        days: options.days,
        regressionWindowDays: options.regressionWindowDays,
      }),
    ]);
    const envs = CommitAnalyzer.runEnvironments(span.commits);
    const timelines = TimelineSplit.deserialize(options.timelineSplit).timelines(envs);
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.parse(options.fql ?? '') });
    const result = await this._reportCounters.get({
      span,
      history,
      filter,
      timelines,
    });
    assert(result);
    return result;
  }

  public async testsReport(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    dailyOutcomesDays: number,
  }): Promise<TestsReport> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const commits = repo?.iterator(options.commitOptions.head).collect(options.commitOptions) ?? [];
    const lastCommit = commits.at(-1);
    if (!lastCommit)
      return new TestsReport(new TestIndex(), []);

    const [span, history] = await Promise.all([
      this._fetchSpan(index, commits),
      this._historyAnalyzer(index, repo, {
        head: options.commitOptions.head,
        days: options.days,
        regressionWindowDays: options.regressionWindowDays,
      }),
    ]);
    const envs = CommitAnalyzer.runEnvironments(span.commits);
    const timelines = TimelineSplit.deserialize(options.timelineSplit).timelines(envs);
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.parse(options.fql ?? '') });
    const report = await this._testsReport.get({
      testIndex: index.testIndex(),
      span,
      history,
      filter,
      timelines,
      dailyOutcomesDays: options.dailyOutcomesDays,
    });
    assert(report);
    return report;
  }

  public async commitsReport(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    testId?: Stats.TestId,
    days: WireTypes.Day[],
    regressionWindowDays: number,
  }): Promise<WireTypes.CommitStats[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);

    const allCommits = repo?.iterator(options.commitOptions.head).collect(options.commitOptions) ?? [];

    const [span, history] = await Promise.all([
       this._fetchSpan(index, allCommits),
       this._historyAnalyzer(index, repo, {
         head: options.commitOptions.head,
         days: options.days,
         regressionWindowDays: options.regressionWindowDays,
       }),
    ]);

    const filter = this._filterContexts.get({
      testIndex: index.testIndex(),
      fql: options.testId ? Query.create({ testId: options.testId }) : Query.EMPTY,
    });

    const split = TimelineSplit.deserialize(options.timelineSplit);
    const timelines = split.timelines(CommitAnalyzer.runEnvironments(span.commits));
    return this._commitsReport.get({
      analyzers: span.commits,
      commits: allCommits,
      timelines,
      filter,
      history,
    });
  }

  public async dailyUnfilteredTestOutcomes(options: {
    head: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    cutoffTestIndex: Stats.TestIndex,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes[], commitCounts: number[], durations: Histogram.CompressedHistogram[][] }[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const history = await this._historyAnalyzer(index, repo, options);
    return options.timelines.map(jsonTimeline => {
      const timeline = Timeline.deserialize(jsonTimeline);
      return {
        timeline: timeline.serialize(),
        testOutcomes: history.days().map((commits, dayIdx) => {
          return T.capAt(history.dayStats(timeline, dayIdx, true).testOutcomes, options.cutoffTestIndex);
        }),
        commitCounts: history.days().map(day => day.commits.length),
        durations: history.days().map((commits, dayIdx) => {
          const s = history.dayStats(timeline, dayIdx, true);
          return s.durations.map(h => Histogram.compress(h));
        }),
      };
    });
  }

  public async dailyOutcomes(options: {
    head: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
    timelineSplit: WireTypes.JSONTimelineSplit,
    resultDays: number,
    testId?: Stats.TestId,
  }): Promise<WireTypes.DayOutcome[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const history = await this._historyAnalyzer(index, repo, options);
    const split = TimelineSplit.deserialize(options.timelineSplit);
    const days = await this._dailyOutcomes.get({
      testIndex: index.testIndex(),
      testId: options.testId,
      history,
      resultDays: options.resultDays,
      timelines: split.timelines(history.environments()),
    });
    assert(days);
    return days;
  }

  public async commitUnfilteredTestOutcomes(options: {
    regressionWindowDays: number,
    days: WireTypes.Day[],
    cutoffTestIndex: Stats.TestIndex,
    commitId: Stats.CommitId,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes }[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const history = await this._historyAnalyzer(index, repo, {
      head: options.commitId,
      days: options.days,
      regressionWindowDays: options.regressionWindowDays,
    });
    return options.timelines.map(timelineJson => {
      const timeline = Timeline.deserialize(timelineJson);
      return {
        timeline: timeline.serialize(),
        testOutcomes: T.capAt(history.commitStats(timeline, options.commitId, false).testOutcomes, options.cutoffTestIndex),
      };
    });
  }

  private async _fetchSpan(index: ReportIndex.ReportIndex, commits: Git.Commit[]): Promise<SpanAnalyzer> {
    const shards = index.splitIntoShards(commits);
    const analyzers = new Map<Stats.CommitId, CommitAnalyzer>();
    await Promise.all([...shards].map(async ([s3id, commits]) => {
      const statsAnalyzer = await this._getStatsAnalyzer(s3id);
      let time = performance.now();
      for (const commit of commits) {
        analyzers.set(commit.commitId, this._commitAnalyzers.get({ commitId: commit.commitId, stats: statsAnalyzer }));
        if (performance.now() - time > 100) {
          await new Promise(x => process.nextTick(x));
          time = performance.now();
        }
      }
    }));
    const commitAnalyzers = commits.map(commit => analyzers.get(commit.commitId) ?? CommitAnalyzer.createEmpty(commit.commitId));
    return this._spanAnalyzers.get(commitAnalyzers);
  }

  private async _historyAnalyzer(index: ReportIndex.ReportIndex, repo: Git.Repository|undefined, options: {
    head: string,
    days: WireTypes.Day[],
    regressionWindowDays: number,
  }): Promise<HistoryAnalyzer> {
    // Make sure days are in reversed order.
    const days = options.days.toSorted((d1, d2) => d2.sinceTimestamp - d1.sinceTimestamp);
    const iterator = repo?.iterator(options.head);
    if (!days.length || !iterator)
      return new HistoryAnalyzer([], options.regressionWindowDays);
    const historyDays = days.map(day => iterator.collect(day));
    const spans = await Promise.all(historyDays.map(commits => this._fetchSpan(index, commits)));

    return this._historyAnalyzers.get({
      historyDays: spans,
      regressionWindowDays: options.regressionWindowDays,
    });
  }
}
