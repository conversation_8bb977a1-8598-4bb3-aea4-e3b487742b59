import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlCheckbox, SlDialog, SlInputEvent } from '@shoelace-style/shoelace';
import { api } from './api.js';
import { docLinkStyles, linkStyles, pageStyles } from './components/cssstyles.js';
import { GithubInstallation } from './components/pick-github-installation.js';
import { GithubPat } from './components/pick-github-pat.js';
import { PickProjectName, ProjectNamePicked } from './components/pick-project-name.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts, FKProjectChanged } from './contexts.js';
import { githubLinks } from './githubLinks.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';
import { assert } from './utils.js';

@customElement('page-project-superadm')
export class PageProjectSuperadm extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'superadm'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/superadm',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-superadm
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-superadm>
      `),
    }];
  }

  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  
  @query('#link-dialog') private _linkDialog?: SlDialog;
  @query('pick-project-name') private _projectName?: PickProjectName;

  @state() private _githubPat?: GithubPat;
  @state() private _githubApp?: GithubInstallation;
  @state() private _projectRename?: ProjectNamePicked;

  private _fetchedRepositoryStats = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug] as const,
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      return await api.project.fetchRepositoryStats.GET({ orgSlug, projectSlug }, { signal });
    },
  });

  render() {
    if (!this._project)
      return nothing;

    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"superadm"}
      ></project-header>
      <app-body user-role=${this._project.access}>
        <h1>Super Administration</h1>

        ${this._user?.isSuperUser ? html`
          <form-section label="Project Public ID">
            <sl-copy-button id=copy-button value=${this._project.projectPublicId}></sl-copy-button>
            <sl-tag size=medium>${this._project.projectPublicId}</sl-tag>
          </form-section>
          <form-section label="Advanced Settings" is-editor>
            <sl-checkbox ?checked=${this._project?.reportTimeIsUploadTime} @input=${async (event: SlInputEvent) => {
              if (!this.orgSlug || !this.projectSlug)
                return;
              await api.project.setProjectReportTimeAsUploadTimeForTest.POST({
                orgSlug: this.orgSlug,
                projectSlug: this.projectSlug,
                reportTimeAsUploadTime: (event.target as SlCheckbox).checked,
              });
              this.dispatchEvent(new FKProjectChanged({
                orgSlug: this.orgSlug,
                projectSlug: this.projectSlug,
              }));
            }}>Treat report time as upload time</sl-checkbox>
          </form-section>
          <form-section label="Git Repository">
            <p style="gap: var(--sl-spacing-2x-small);">
              <a-ext href=${githubLinks.projectUrl(this._project)}>${this._project.sourceOwner}/${this._project.sourceRepo}</a-ext>
            </p>
            <p>
              ${this._renderLastFetchAttempt(this._project)}
            </p>
            <p>
              <sl-button @click=${async () => {
                if (!this.orgSlug || !this.projectSlug)
                  return;
                await api.administration.requestGitFetch.GET({
                  orgSlug: this.orgSlug,
                  projectSlug: this.projectSlug,
                });
                this._fetchedRepositoryStats.run();
              }}>Request Git Fetch</sl-button>
            </p>
          </form-section>
        ` : nothing}

      </app-body>
      <app-footer></app-footer>
    `
  }

  private _renderLastFetchAttempt(project: WireTypes.Project) {
    const { commits, branches, pullRequests } = this._fetchedRepositoryStats.value ?? {};
    if (!project.sourceLastFetchHTTPCode || !project.sourceLastFetchTime || commits === undefined || branches === undefined)
      return html`<span>fetching commits right now...</span>`;
    const relativeTime = html`
      <sl-tooltip>
        <sl-format-date slot="content" month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(project.sourceLastFetchTime).toISOString()}></sl-format-date>
        <b><sl-relative-time date=${new Date(project.sourceLastFetchTime).toISOString()}></sl-relative-time></b>
      </sl-tooltip>
    `;

    const info = html`Fetched <b>${commits}</b> commits, <b>${branches}</b> branches and <b>${pullRequests}</b> pull requests.`;
    const attempt = (project.sourceLastFetchHTTPCode === 200) ?
      html`<b>Successfully</b> pulled history ${relativeTime}` :
      html`<sl-tooltip content="Github responded with HTTP ${project.sourceLastFetchHTTPCode}"><b>Failed</b></sl-tooltip> to pull history ${relativeTime}`
    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <div>${info}</div>
        <div>${attempt}</div>
      </v-box>
    `
  }

  static styles = [linkStyles, githubMarkdownCSS, docLinkStyles, pageStyles, css`
    a-ext {
      color: rgb(9, 105, 218);
    }
    [user-role=viewer] {
      *:is([is-editor]) {
        display: none;
      }
    }
  `];
}
